from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_required, current_user
from app import db
from app.models import Supplier, SupplierProduct, Ingredient, ProductSpecParameter, SupplierSchoolRelation
from app.forms.supplier import SupplierProductForm, ProductSpecParameterForm
from app.utils.log_activity import log_activity
from sqlalchemy import text
import os
from datetime import datetime
import uuid
import json

supplier_product_bp = Blueprint('supplier_product', __name__)

@supplier_product_bp.route('/')
@login_required
def index():
    """供应商产品列表页面"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    supplier_id = request.args.get('supplier_id', type=int)
    ingredient_id = request.args.get('ingredient_id', type=int)
    status = request.args.get('status', type=int)
    keyword = request.args.get('keyword', '')

    # 基本查询
    query = SupplierProduct.query

    if supplier_id:
        query = query.filter_by(supplier_id=supplier_id)

    if ingredient_id:
        query = query.filter_by(ingredient_id=ingredient_id)

    if status is not None:
        query = query.filter_by(is_available=status)

    if keyword:
        query = query.join(Ingredient).filter(
            db.or_(
                SupplierProduct.product_name.like(f'%{keyword}%'),
                Ingredient.name.like(f'%{keyword}%'),
                SupplierProduct.model_number.like(f'%{keyword}%')
            )
        )

    # 根据用户区域权限筛选产品
    if not current_user.is_admin():
        # 获取用户可访问的区域ID列表
        accessible_area_ids = [area.id for area in current_user.get_accessible_areas()]

        # 通过供应商-学校关联表筛选供应商产品
        query = query.join(Supplier, SupplierProduct.supplier_id == Supplier.id)\
                    .join(SupplierSchoolRelation, Supplier.id == SupplierSchoolRelation.supplier_id)\
                    .filter(SupplierSchoolRelation.area_id.in_(accessible_area_ids))\
                    .filter(SupplierSchoolRelation.status == 1)\
                    .distinct()

    pagination = query.order_by(SupplierProduct.id.desc()).paginate(page=page, per_page=per_page)
    products = pagination.items

    # 获取供应商列表，也需要根据用户区域权限筛选
    if current_user.is_admin():
        suppliers = Supplier.query.all()
    else:
        # 获取用户可访问的区域ID列表
        accessible_area_ids = [area.id for area in current_user.get_accessible_areas()]

        # 通过供应商-学校关联表筛选供应商
        suppliers = Supplier.query.join(SupplierSchoolRelation)\
                    .filter(SupplierSchoolRelation.area_id.in_(accessible_area_ids))\
                    .filter(SupplierSchoolRelation.status == 1)\
                    .distinct().all()

    ingredients = Ingredient.query.all()

    # 获取当前用户的区域信息
    current_area = current_user.get_current_area()
    area_path = []
    if current_area:
        area_path = [current_area]
        ancestors = current_area.get_ancestors()
        area_path = ancestors + area_path

    return render_template('supplier/product_index.html',
                          products=products,
                          pagination=pagination,
                          suppliers=suppliers,
                          ingredients=ingredients,
                          supplier_id=supplier_id,
                          ingredient_id=ingredient_id,
                          status=status,
                          keyword=keyword,
                          current_area=current_area,
                          area_path=area_path,
                          title='供应商产品管理',
                          now=datetime.now())

@supplier_product_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    """添加供应商产品"""
    form = SupplierProductForm()

    # 获取供应商选项，根据用户权限筛选
    if current_user.is_admin():
        # 系统管理员可以看到所有供应商
        suppliers = Supplier.query.filter_by(status=1).all()
    else:
        # 普通用户只能看到与自己管辖学校有合作关系的供应商
        accessible_area_ids = [area.id for area in current_user.get_accessible_areas()]

        # 通过供应商-学校关联表筛选供应商
        suppliers = Supplier.query.join(SupplierSchoolRelation)\
                    .filter(SupplierSchoolRelation.area_id.in_(accessible_area_ids))\
                    .filter(Supplier.status == 1)\
                    .filter(SupplierSchoolRelation.status == 1)\
                    .distinct().all()

    form.supplier_id.choices = [(0, '-- 请选择供应商 --')] + [(s.id, s.name) for s in suppliers]

    # 获取食材选项
    ingredients = Ingredient.query.filter_by(status=1).all()  # 只显示启用的食材
    form.ingredient_id.choices = [(0, '-- 请选择食材 --')] + [(i.id, f"{i.name} ({i.unit})" if i.unit else i.name) for i in ingredients]

    if form.validate_on_submit():
        # 处理产品图片上传
        product_image = None
        if form.product_image.data:
            image_filename = str(uuid.uuid4()) + os.path.splitext(form.product_image.data.filename)[1]
            image_path = os.path.join('img', 'products', image_filename)
            os.makedirs(os.path.join(current_app.static_folder, 'img', 'products'), exist_ok=1)
            form.product_image.data.save(os.path.join(current_app.static_folder, image_path))
            product_image = image_path

        product = SupplierProduct(
            supplier_id=form.supplier_id.data,
            ingredient_id=form.ingredient_id.data,
            product_code=form.product_code.data,
            product_name=form.product_name.data,
            model_number=form.model_number.data,
            specification=form.specification.data,
            price=form.price.data,
            quality_cert=form.quality_cert.data,
            quality_standard=form.quality_standard.data,
            product_image=product_image,
            lead_time=form.lead_time.data,
            min_order_quantity=form.min_order_quantity.data,
            description=form.description.data,
            is_available=0,  # 默认未上架
            shelf_status=0   # 默认待审核
        )
        db.session.add(product)

        # 添加审计日志
        log_activity(
            action='create',
            resource_type='SupplierProduct',
            resource_id=product.id,
            details={
                'supplier_id': product.supplier_id,
                'ingredient_id': product.ingredient_id,
                'product_name': product.product_name,
                'price': str(product.price)
            }
        )

        db.session.commit()
        flash('供应商产品添加成功！产品需要审核后才能上架。', 'success')
        return redirect(url_for('supplier_product.index'))

    return render_template('supplier/product_form.html', form=form, title='添加供应商产品', now=datetime.now())

@supplier_product_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """编辑供应商产品"""
    product = SupplierProduct.query.get_or_404(id)
    form = SupplierProductForm(obj=product)

    # 获取供应商选项，根据用户权限筛选
    if current_user.is_admin():
        # 系统管理员可以看到所有供应商
        suppliers = Supplier.query.filter_by(status=1).all()
    else:
        # 普通用户只能看到与自己管辖学校有合作关系的供应商
        accessible_area_ids = [area.id for area in current_user.get_accessible_areas()]

        # 通过供应商-学校关联表筛选供应商
        suppliers = Supplier.query.join(SupplierSchoolRelation)\
                    .filter(SupplierSchoolRelation.area_id.in_(accessible_area_ids))\
                    .filter(Supplier.status == 1)\
                    .filter(SupplierSchoolRelation.status == 1)\
                    .distinct().all()

    form.supplier_id.choices = [(0, '-- 请选择供应商 --')] + [(s.id, s.name) for s in suppliers]

    # 获取食材选项
    ingredients = Ingredient.query.filter_by(status=1).all()  # 只显示启用的食材
    form.ingredient_id.choices = [(0, '-- 请选择食材 --')] + [(i.id, i.name) for i in ingredients]

    if form.validate_on_submit():
        old_data = {
            'supplier_id': product.supplier_id,
            'ingredient_id': product.ingredient_id,
            'product_code': product.product_code,
            'product_name': product.product_name,
            'model_number': product.model_number,
            'specification': product.specification,
            'price': str(product.price),
            'quality_cert': product.quality_cert,
            'quality_standard': product.quality_standard,
            'lead_time': product.lead_time,
            'min_order_quantity': product.min_order_quantity,
            'description': product.description
        }

        # 处理产品图片上传
        if form.product_image.data:
            # 删除旧图片
            if product.product_image:
                old_image_path = os.path.join(current_app.static_folder, product.product_image)
                if os.path.exists(old_image_path):
                    os.remove(old_image_path)

            # 保存新图片
            image_filename = str(uuid.uuid4()) + os.path.splitext(form.product_image.data.filename)[1]
            image_path = os.path.join('img', 'products', image_filename)
            os.makedirs(os.path.join(current_app.static_folder, 'img', 'products'), exist_ok=1)
            form.product_image.data.save(os.path.join(current_app.static_folder, image_path))
            product.product_image = image_path

        product.supplier_id = form.supplier_id.data
        product.ingredient_id = form.ingredient_id.data
        product.product_code = form.product_code.data
        product.product_name = form.product_name.data
        product.model_number = form.model_number.data
        product.specification = form.specification.data
        product.price = form.price.data
        product.quality_cert = form.quality_cert.data
        product.quality_standard = form.quality_standard.data
        product.lead_time = form.lead_time.data
        product.min_order_quantity = form.min_order_quantity.data
        product.description = form.description.data
        product.shelf_status = 0  # 修改后重新设为待审核状态
        product.is_available = 0  # 修改后重新设为未上架状态

        # 添加审计日志
        log_activity(
            action='update',
            resource_type='SupplierProduct',
            resource_id=product.id,
            details={
                'old': old_data,
                'new': {
                    'supplier_id': product.supplier_id,
                    'ingredient_id': product.ingredient_id,
                    'product_code': product.product_code,
                    'product_name': product.product_name,
                    'model_number': product.model_number,
                    'specification': product.specification,
                    'price': str(product.price),
                    'quality_cert': product.quality_cert,
                    'quality_standard': product.quality_standard,
                    'lead_time': product.lead_time,
                    'min_order_quantity': product.min_order_quantity,
                    'description': product.description
                }
            }
        )

        db.session.commit()
        flash('供应商产品更新成功！产品需要重新审核后才能上架。', 'success')
        return redirect(url_for('supplier_product.index'))

    return render_template('supplier/product_form.html', form=form, product=product, title='编辑供应商产品', now=datetime.now())

@supplier_product_bp.route('/<int:id>/view')
@login_required
def view(id):
    """查看供应商产品详情"""
    product = SupplierProduct.query.get_or_404(id)
    spec_parameters = ProductSpecParameter.query.filter_by(product_id=id).all()

    # 添加规格参数表单
    param_form = ProductSpecParameterForm()
    param_form.product_id.data = id

    # 添加审计日志
    log_activity(
        action='view',
        resource_type='SupplierProduct',
        resource_id=product.id
    )

    return render_template('supplier/product_view.html',
                          product=product,
                          spec_parameters=spec_parameters,
                          param_form=param_form,
                          title='供应商产品详情',
                          now=datetime.now())

@supplier_product_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """删除供应商产品"""
    product = SupplierProduct.query.get_or_404(id)

    # 检查是否有关联的采购订单项
    # 这里需要根据实际情况添加检查逻辑
    # if PurchaseOrderItem.query.filter_by(product_id=id).first():
    #     return jsonify({'success': 0, 'message': '该产品已关联采购订单，不能删除！'})

    # 删除产品图片
    if product.product_image:
        image_path = os.path.join(current_app.static_folder, product.product_image)
        if os.path.exists(image_path):
            os.remove(image_path)

    # 删除产品规格参数
    ProductSpecParameter.query.filter_by(product_id=id).delete()

    # 添加审计日志
    log_activity(
        action='delete',
        resource_type='SupplierProduct',
        resource_id=product.id,
        details={
            'supplier_id': product.supplier_id,
            'ingredient_id': product.ingredient_id,
            'product_name': product.product_name or product.ingredient.name,
            'price': str(product.price)
        }
    )

    db.session.delete(product)
    db.session.commit()

    return jsonify({'success': 1, 'message': '供应商产品删除成功！'})

@supplier_product_bp.route('/shelf/<int:id>', methods=['POST'])
@login_required
def shelf_product(id):
    """产品上架"""
    product = SupplierProduct.query.get_or_404(id)

    # 检查产品是否已审核
    if product.shelf_status != 1:
        return jsonify({'success': 0, 'message': '产品未审核通过，不能上架！'})

    # 更新产品状态
    product.is_available = 1
    product.shelf_time = datetime.now()
    product.shelf_operator_id = current_user.id

    # 添加审计日志
    log_activity(
        action='shelf',
        resource_type='SupplierProduct',
        resource_id=product.id,
        details={
            'product_id': product.id,
            'product_name': product.product_name or product.ingredient.name,
            'shelf_time': product.shelf_time.strftime("%Y-%m-%d %H:%M")
        }
    )

    db.session.commit()
    return jsonify({'success': 1, 'message': '产品上架成功！'})

@supplier_product_bp.route('/unshelf/<int:id>', methods=['POST'])
@login_required
def unshelf_product(id):
    """产品下架"""
    product = SupplierProduct.query.get_or_404(id)

    # 更新产品状态
    product.is_available = 0

    # 添加审计日志
    log_activity(
        action='unshelf',
        resource_type='SupplierProduct',
        resource_id=product.id,
        details={
            'product_id': product.id,
            'product_name': product.product_name or product.ingredient.name,
            'unshelf_time': datetime.now().strftime("%Y-%m-%d %H:%M")
        }
    )

    db.session.commit()
    return jsonify({'success': 1, 'message': '产品下架成功！'})

@supplier_product_bp.route('/approve/<int:id>', methods=['POST'])
@login_required
def approve_product(id):
    """审核通过产品"""
    product = SupplierProduct.query.get_or_404(id)

    # 更新产品状态
    product.shelf_status = 1  # 已审核

    # 添加审计日志
    log_activity(
        action='approve',
        resource_type='SupplierProduct',
        resource_id=product.id,
        details={
            'product_id': product.id,
            'product_name': product.product_name or product.ingredient.name,
            'approve_time': datetime.now().strftime("%Y-%m-%d %H:%M")
        }
    )

    db.session.commit()
    return jsonify({'success': 1, 'message': '产品审核通过！'})

@supplier_product_bp.route('/reject/<int:id>', methods=['POST'])
@login_required
def reject_product(id):
    """拒绝产品上架"""
    product = SupplierProduct.query.get_or_404(id)
    reason = request.form.get('reason', '')

    # 更新产品状态
    product.shelf_status = 2  # 已拒绝

    # 添加审计日志
    log_activity(
        action='reject',
        resource_type='SupplierProduct',
        resource_id=product.id,
        details={
            'product_id': product.id,
            'product_name': product.product_name or product.ingredient.name,
            'reject_time': datetime.now().strftime("%Y-%m-%d %H:%M"),
            'reason': reason
        }
    )

    db.session.commit()
    return jsonify({'success': 1, 'message': '已拒绝产品上架申请！'})

@supplier_product_bp.route('/add_parameter', methods=['POST'])
@login_required
def add_parameter():
    """添加产品规格参数"""
    form = ProductSpecParameterForm()

    if form.validate_on_submit():
        parameter = ProductSpecParameter(
            product_id=form.product_id.data,
            param_name=form.param_name.data,
            param_value=form.param_value.data,
            param_unit=form.param_unit.data
        )
        db.session.add(parameter)

        # 添加审计日志
        log_activity(
            action='create',
            resource_type='ProductSpecParameter',
            resource_id=parameter.id,
            details={
                'product_id': parameter.product_id,
                'param_name': parameter.param_name,
                'param_value': parameter.param_value,
                'param_unit': parameter.param_unit
            }
        )

        db.session.commit()
        flash('产品规格参数添加成功！', 'success')
    else:
        flash('参数添加失败，请检查输入！', 'danger')

    return redirect(url_for('supplier_product.view', id=form.product_id.data))

@supplier_product_bp.route('/delete_parameter/<int:id>', methods=['POST'])
@login_required
def delete_parameter(id):
    """删除产品规格参数"""
    parameter = ProductSpecParameter.query.get_or_404(id)

    # 添加审计日志
    log_activity(
        action='delete',
        resource_type='ProductSpecParameter',
        resource_id=parameter.id,
        details={
            'product_id': parameter.product_id,
            'param_name': parameter.param_name,
            'param_value': parameter.param_value,
            'param_unit': parameter.param_unit
        }
    )

    db.session.delete(parameter)
    db.session.commit()

    return jsonify({'success': 1, 'message': '产品规格参数删除成功！'})

@supplier_product_bp.route('/api')
@login_required
def api_list():
    """供应商产品API"""
    supplier_id = request.args.get('supplier_id', type=int)
    ingredient_id = request.args.get('ingredient_id', type=int)
    is_available = request.args.get('is_available', type=int)

    query = SupplierProduct.query

    if supplier_id:
        query = query.filter_by(supplier_id=supplier_id)

    if ingredient_id:
        query = query.filter_by(ingredient_id=ingredient_id)

    if is_available is not None:
        query = query.filter_by(is_available=is_available)

    products = query.all()
    return jsonify([{
        'id': p.id,
        'supplier_id': p.supplier_id,
        'supplier_name': p.supplier.name,
        'ingredient_id': p.ingredient_id,
        'ingredient_name': p.ingredient.name,
        'product_name': p.product_name or p.ingredient.name,
        'model_number': p.model_number,
        'specification': p.specification,
        'price': float(p.price),
        'is_available': p.is_available
    } for p in products])

@supplier_product_bp.route('/api/ingredient/<int:id>')
@login_required
def api_ingredient_detail(id):
    """获取食材详细信息API"""
    ingredient = Ingredient.query.get_or_404(id)

    # 记录查看日志
    log_activity(
        action='view',
        resource_type='Ingredient',
        resource_id=ingredient.id
    )

    # 返回食材详细信息
    return jsonify({
        'id': ingredient.id,
        'name': ingredient.name,
        'category': ingredient.category,
        'category_id': ingredient.category_id,
        'category_name': ingredient.category_rel.name if ingredient.category_rel else None,
        'unit': ingredient.unit,
        'standard_unit': getattr(ingredient, 'standard_unit', None),
        'specification': ingredient.specification,
        'storage_temp': ingredient.storage_temp,
        'storage_condition': ingredient.storage_condition,
        'shelf_life': ingredient.shelf_life,
        'is_condiment': getattr(ingredient, 'is_condiment', False),
        'status': ingredient.status
    })

@supplier_product_bp.route('/api/generate-product-code')
@login_required
def generate_product_code():
    """生成产品编码API"""
    supplier_id = request.args.get('supplier_id', type=int)

    # 基础编码前缀
    prefix = "SP"

    # 如果提供了供应商ID，使用供应商简称作为前缀
    if supplier_id:
        supplier = Supplier.query.get(supplier_id)
        if supplier:
            # 使用供应商名称的首字母作为前缀
            # 如果是中文，取第一个字的拼音首字母
            import pypinyin
            if supplier.name and len(supplier.name) > 0:
                # 检查是否是中文
                if '\u4e00' <= supplier.name[0] <= '\u9fff':
                    # 获取拼音首字母
                    pinyin = pypinyin.lazy_pinyin(supplier.name[0], style=pypinyin.FIRST_LETTER)
                    if pinyin and pinyin[0]:
                        prefix = pinyin[0].upper()
                else:
                    # 非中文，直接取首字母
                    prefix = supplier.name[0].upper()

    # 查询最后一个产品编码
    last_product = SupplierProduct.query.filter(
        SupplierProduct.product_code.like(f"{prefix}-%")
    ).order_by(SupplierProduct.id.desc()).first()

    # 生成新编码
    if last_product and last_product.product_code:
        try:
            # 尝试解析最后一个编码的数字部分
            code_parts = last_product.product_code.split('-')
            if len(code_parts) > 1:
                last_number = int(code_parts[1])
                new_number = last_number + 1
            else:
                new_number = 1
        except (ValueError, IndexError):
            new_number = 1
    else:
        new_number = 1

    # 格式化编码，确保数字部分至少有3位
    new_code = f"{prefix}-{new_number:03d}"

    return jsonify({
        'product_code': new_code
    })

@supplier_product_bp.route('/batch-operation', methods=['POST'])
@login_required
def batch_operation():
    """批量操作产品"""
    try:
        data = request.get_json()
        operation = data.get('operation')
        product_ids = data.get('product_ids', [])

        if not operation or not product_ids:
            return jsonify({'success': False, 'message': '参数不完整'}), 400

        # 转换产品ID为整数类型
        try:
            product_ids = [int(pid) for pid in product_ids]
        except (ValueError, TypeError):
            return jsonify({'success': False, 'message': '产品ID格式错误'}), 400

        # 验证产品ID
        products = SupplierProduct.query.filter(SupplierProduct.id.in_(product_ids)).all()
        if not products:
            return jsonify({'success': False, 'message': '未找到指定的产品'}), 404

        processed_count = 0

        if operation == 'approve':
            # 批量审核通过
            for product in products:
                if product.shelf_status == 0:  # 只处理待审核的产品
                    update_sql = text("""
                        UPDATE supplier_products
                        SET shelf_status = 1, updated_at = GETDATE()
                        WHERE id = :product_id
                    """)
                    db.session.execute(update_sql, {'product_id': product.id})
                    processed_count += 1

                    # 记录日志
                    log_activity(
                        action='batch_approve',
                        resource_type='SupplierProduct',
                        resource_id=product.id,
                        details={'batch_operation': True}
                    )

        elif operation == 'reject':
            # 批量拒绝
            reason = data.get('reason', '批量拒绝')
            for product in products:
                if product.shelf_status == 0:  # 只处理待审核的产品
                    update_sql = text("""
                        UPDATE supplier_products
                        SET shelf_status = 2, updated_at = GETDATE()
                        WHERE id = :product_id
                    """)
                    db.session.execute(update_sql, {'product_id': product.id})
                    processed_count += 1

                    # 记录日志
                    log_activity(
                        action='batch_reject',
                        resource_type='SupplierProduct',
                        resource_id=product.id,
                        details={'batch_operation': True, 'reason': reason}
                    )

        elif operation == 'shelf':
            # 批量上架
            for product in products:
                if product.shelf_status == 1 and product.is_available == 0:  # 只处理已审核且未上架的产品
                    update_sql = text("""
                        UPDATE supplier_products
                        SET is_available = 1, shelf_time = GETDATE(), shelf_operator_id = :operator_id, updated_at = GETDATE()
                        WHERE id = :product_id
                    """)
                    db.session.execute(update_sql, {'product_id': product.id, 'operator_id': current_user.id})
                    processed_count += 1

                    # 记录日志
                    log_activity(
                        action='batch_shelf',
                        resource_type='SupplierProduct',
                        resource_id=product.id,
                        details={'batch_operation': True}
                    )

        elif operation == 'unshelf':
            # 批量下架
            for product in products:
                if product.is_available == 1:  # 只处理已上架的产品
                    update_sql = text("""
                        UPDATE supplier_products
                        SET is_available = 0, updated_at = GETDATE()
                        WHERE id = :product_id
                    """)
                    db.session.execute(update_sql, {'product_id': product.id})
                    processed_count += 1

                    # 记录日志
                    log_activity(
                        action='batch_unshelf',
                        resource_type='SupplierProduct',
                        resource_id=product.id,
                        details={'batch_operation': True}
                    )

        elif operation == 'delete':
            # 批量删除
            for product in products:
                # 记录日志
                log_activity(
                    action='batch_delete',
                    resource_type='SupplierProduct',
                    resource_id=product.id,
                    details={'batch_operation': True, 'product_name': product.product_name or product.ingredient.name}
                )

                # 删除相关的规格参数
                ProductSpecParameter.query.filter_by(product_id=product.id).delete()

                # 删除产品
                db.session.delete(product)
                processed_count += 1

        else:
            return jsonify({'success': False, 'message': '不支持的操作类型'}), 400

        db.session.commit()

        operation_names = {
            'approve': '审核通过',
            'reject': '拒绝',
            'shelf': '上架',
            'unshelf': '下架',
            'delete': '删除'
        }

        return jsonify({
            'success': True,
            'message': f'批量{operation_names[operation]}成功',
            'processed_count': processed_count
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'批量操作失败: {str(e)}')
        return jsonify({'success': False, 'message': '操作失败，请稍后重试'}), 500

@supplier_product_bp.route('/available-ingredients')
@login_required
def available_ingredients():
    """获取用户可访问的供应商上架食材"""
    try:
        # 获取当前用户可访问的区域
        accessible_areas = current_user.get_accessible_areas()
        area_ids = [area.id for area in accessible_areas]

        # 查询供应商上架的食材
        ingredients_query = db.session.query(Ingredient).join(
            SupplierProduct, Ingredient.id == SupplierProduct.ingredient_id
        ).join(
            Supplier, SupplierProduct.supplier_id == Supplier.id
        ).join(
            SupplierSchoolRelation, Supplier.id == SupplierSchoolRelation.supplier_id
        ).filter(
            SupplierSchoolRelation.area_id.in_(area_ids),
            SupplierSchoolRelation.status == 1,
            Supplier.status == 1,
            SupplierProduct.is_available == 1,
            SupplierProduct.shelf_status == 1,
            Ingredient.status == 1
        ).distinct()

        # 获取查询参数
        search = request.args.get('search', '').strip()
        category = request.args.get('category', '').strip()

        # 应用过滤
        if search:
            ingredients_query = ingredients_query.filter(Ingredient.name.like(f'%{search}%'))

        if category and category != '全部':
            ingredients_query = ingredients_query.filter(Ingredient.category == category)

        # 获取结果
        ingredients = ingredients_query.order_by(Ingredient.name).all()

        # 构建响应数据
        ingredients_data = []
        for ingredient in ingredients:
            # 统计该食材的供应商数量
            supplier_count = db.session.query(SupplierProduct).join(
                Supplier, SupplierProduct.supplier_id == Supplier.id
            ).join(
                SupplierSchoolRelation, Supplier.id == SupplierSchoolRelation.supplier_id
            ).filter(
                SupplierProduct.ingredient_id == ingredient.id,
                SupplierSchoolRelation.area_id.in_(area_ids),
                SupplierSchoolRelation.status == 1,
                Supplier.status == 1,
                SupplierProduct.is_available == 1,
                SupplierProduct.shelf_status == 1
            ).count()

            ingredients_data.append({
                'id': ingredient.id,
                'name': ingredient.name,
                'unit': ingredient.unit or '',
                'category': ingredient.category or '未分类',
                'has_supplier': True,
                'source': '供应商',
                'supplier_count': supplier_count
            })

        return jsonify({
            'success': True,
            'ingredients': ingredients_data
        })

    except Exception as e:
        current_app.logger.error(f'获取可用食材失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': '获取食材数据失败'
        })
