{% extends 'base.html' %}

{% block title %}采购订单详情 - {{ order.order_number }} - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题和操作按钮 -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2>采购订单详情</h2>
            <p class="text-muted">订单号：{{ order.order_number }}</p>
        </div>
        <div class="col-md-4 text-right">
            <div class="btn-action-group">
                <a href="{{ url_for('purchase_order.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> 返回列表
                </a>
                <a href="{{ url_for('purchase_order.print_order', order_id=order.id) }}"
                   class="btn btn-outline-primary"
                   target="_blank">
                    <i class="fas fa-print"></i> 打印订单
                </a>

                <!-- 管理员操作下拉菜单 -->
                {% if current_user.is_admin() or current_user.has_role('学校管理员') %}
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-toggle="dropdown">
                        <i class="fas fa-cog"></i> 管理操作
                    </button>
                    <div class="dropdown-menu dropdown-menu-right">
                        {% if not order.has_stock_in %}
                        <button class="dropdown-item delete-btn" data-id="{{ order.id }}">
                            <i class="fas fa-trash-alt text-danger"></i> 删除订单
                        </button>
                        {% endif %}
                        <div class="dropdown-divider"></div>
                        <h6 class="dropdown-header">订单状态</h6>
                        <span class="dropdown-item-text">
                            <small class="text-muted">当前状态：{{ order.status_display }}</small>
                        </span>
                    </div>
                </div>
                {% endif %}
            </div>

        </div>
    </div>

    <!-- 订单处理流程 -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">订单处理流程</h5>
                    <small class="text-muted">点击步骤可执行相应操作</small>
                </div>
                <div class="card-body">
                    <div class="compact-process-flow">
                        <!-- 订单创建 -->
                        <div class="compact-process-step completed">
                            <div class="compact-step-icon">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div class="compact-step-title">订单创建</div>
                            <div class="compact-step-time">{{ order.order_date|format_datetime('%m-%d %H:%M') if order.order_date else '-' }}</div>
                        </div>

                        <!-- 订单确认 -->
                        <div class="compact-process-step {% if order.status in ['已确认', '准备入库'] or order.has_stock_in %}completed{% elif order.status == '待确认' %}active{% else %}pending{% endif %}"
                             {% if order.status == '待确认' and not order.has_stock_in %}data-action="confirm"{% endif %}>
                            <div class="compact-step-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="compact-step-title">订单确认</div>
                            <div class="compact-step-time">
                                {% if order.confirmed_at %}
                                    {{ order.confirmed_at|format_datetime('%m-%d %H:%M') }}
                                {% elif order.status == '待确认' %}
                                    进行中
                                {% else %}
                                    等待中
                                {% endif %}
                            </div>
                        </div>

                        <!-- 订单送达 -->
                        <div class="compact-process-step {% if order.status in ['准备入库', '已入库'] or order.has_stock_in %}completed{% elif order.status == '已确认' %}active{% else %}pending{% endif %}"
                             {% if order.status == '已确认' and not order.has_stock_in %}data-action="deliver"{% endif %}>
                            <div class="compact-step-icon">
                                <i class="fas fa-truck"></i>
                            </div>
                            <div class="compact-step-title">订单送达</div>
                            <div class="compact-step-time">
                                {% if order.delivered_at %}
                                    {{ order.delivered_at|format_datetime('%m-%d %H:%M') }}
                                {% elif order.status == '已确认' %}
                                    进行中
                                {% else %}
                                    等待中
                                {% endif %}
                            </div>
                        </div>

                        <!-- 准备入库 -->
                        <div class="compact-process-step {% if order.status in ['已入库'] or order.has_stock_in %}completed{% elif order.status == '准备入库' %}active{% else %}pending{% endif %}">
                            <div class="compact-step-icon">
                                <i class="fas fa-clipboard-check"></i>
                            </div>
                            <div class="compact-step-title">准备入库</div>
                            <div class="compact-step-time">
                                {% if order.delivered_at %}
                                    {{ order.delivered_at|format_datetime('%m-%d %H:%M') }}
                                {% elif order.status == '准备入库' %}
                                    进行中
                                {% else %}
                                    等待中
                                {% endif %}
                            </div>
                        </div>

                        <!-- 创建入库单 -->
                        <div class="compact-process-step {% if order.has_stock_in %}completed{% elif order.status == '准备入库' %}active{% else %}pending{% endif %}"
                             {% if order.status == '准备入库' and not order.has_stock_in %}data-action="stock-in"{% endif %}>
                            <div class="compact-step-icon">
                                <i class="fas fa-dolly"></i>
                            </div>
                            <div class="compact-step-title">创建入库单</div>
                            <div class="compact-step-time">
                                {% if order.has_stock_in %}
                                    已完成
                                {% elif order.status == '准备入库' %}
                                    进行中
                                {% else %}
                                    等待中
                                {% endif %}
                            </div>
                        </div>

                        <!-- 完成入库 -->
                        <div class="compact-process-step {% if order.has_stock_in and order.active_stock_in and order.active_stock_in.status == '已入库' %}completed{% elif order.has_stock_in %}active{% else %}pending{% endif %}">
                            <div class="compact-step-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="compact-step-title">完成入库</div>
                            <div class="compact-step-time">
                                {% if order.has_stock_in and order.active_stock_in and order.active_stock_in.status == '已入库' %}
                                    {{ order.active_stock_in.updated_at|format_datetime('%m-%d') if order.active_stock_in.updated_at else '-' }}
                                {% elif order.has_stock_in %}
                                    进行中
                                {% else %}
                                    等待中
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- 快捷操作区域 -->
                    <div class="quick-actions">
                        {% if not order.has_stock_in and order.status != '已取消' %}
                        <div class="quick-action-item">
                            <a href="{{ url_for('stock_in_wizard.create_from_purchase_get', purchase_order_id=order.id) }}"
                               class="btn btn-success btn-sm">
                                <i class="fas fa-bolt"></i> 一键入库
                            </a>
                            <small>跳过流程直接入库</small>
                        </div>
                        {% endif %}

                        {% if order.status in ['待确认', '已确认'] and not order.has_stock_in %}
                        <div class="quick-action-item">
                            <button class="btn btn-outline-danger btn-sm cancel-btn">
                                <i class="fas fa-times"></i> 取消订单
                            </button>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- 订单状态卡片 -->
        <div class="col-lg-4 d-flex align-items-end">
            <div class="order-status-card w-100">
                <div class="card border-left-primary">
                    <div class="card-body py-3">
                        <div class="d-flex align-items-center">
                            <div class="status-icon mr-3">
                                {% if order.has_stock_in and order.active_stock_in and order.active_stock_in.status == '已入库' %}
                                    <i class="fas fa-check-circle text-success fa-2x"></i>
                                {% elif order.status == '已取消' %}
                                    <i class="fas fa-times-circle text-danger fa-2x"></i>
                                {% elif order.status == '准备入库' %}
                                    <i class="fas fa-dolly text-primary fa-2x"></i>
                                {% elif order.status == '已确认' %}
                                    <i class="fas fa-truck text-info fa-2x"></i>
                                {% else %}
                                    <i class="fas fa-clock text-warning fa-2x"></i>
                                {% endif %}
                            </div>
                            <div class="status-info">
                                <h6 class="mb-1">
                                    {% if order.has_stock_in and order.active_stock_in and order.active_stock_in.status == '已入库' %}
                                        已完成入库
                                    {% else %}
                                        {{ order.status_display }}
                                    {% endif %}
                                </h6>
                                <small class="text-muted">
                                    {% if order.has_stock_in and order.active_stock_in and order.active_stock_in.status == '已入库' %}
                                        订单流程已完成，食材已入库
                                    {% elif order.status == '已取消' %}
                                        订单已取消
                                    {% elif order.status == '准备入库' %}
                                        食材已送达，等待创建入库单
                                    {% elif order.status == '已确认' %}
                                        订单已确认，等待送达
                                    {% else %}
                                        订单等待确认
                                    {% endif %}
                                </small>
                            </div>
                        </div>

                        <!-- 状态相关的徽章 -->
                        <div class="mt-2">
                            {% if order.status_info %}
                                {% if order.status_info.has_stock_in %}
                                    {% set stock_in_status = order.status_info.stock_in_status %}
                                    {% if stock_in_status == '已入库' %}
                                        <span class="badge badge-success">{{ stock_in_status }}</span>
                                    {% elif stock_in_status == '已审核' %}
                                        <span class="badge badge-info">{{ stock_in_status }}</span>
                                    {% elif stock_in_status == '待审核' %}
                                        <span class="badge badge-warning">{{ stock_in_status }}</span>
                                    {% elif stock_in_status == '已取消' %}
                                        <span class="badge badge-danger">{{ stock_in_status }}</span>
                                    {% else %}
                                        <span class="badge badge-info">{{ stock_in_status }}</span>
                                    {% endif %}
                                {% else %}
                                    <span class="badge badge-secondary">未入库</span>
                                {% endif %}

                                {% if order.status_info.consumption_status != '未消耗' %}
                                    <span class="badge {% if order.status_info.consumption_status == '已消耗' %}badge-success{% else %}badge-warning{% endif %}">
                                        {{ order.status_info.consumption_status }}
                                    </span>
                                {% endif %}
                            {% endif %}
                        </div>

                        <!-- 管理操作按钮 - 突出显示 -->
                        {% if order.has_stock_in and order.active_stock_in %}
                        <div class="mt-3">
                            <a href="{{ url_for('stock_in.view_details', id=order.active_stock_in.id) }}"
                               class="btn btn-info btn-block">
                                <i class="fas fa-eye"></i> 查看入库单
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 订单信息 -->
        <div class="col-md-8">
            <div class="card mb-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">订单基本信息</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>订单编号：</strong>{{ order.order_number }}</p>
                            <p><strong>创建时间：</strong>{{ order.order_date|format_datetime('%Y-%m-%d %H:%M') if order.order_date else '-' }}</p>
                            <p>
                                <strong>订单状态：</strong>
                                {% if order.status == '待确认' %}
                                <span class="purchase-order-status status-pending">待确认</span>
                                {% elif order.status == '已确认' %}
                                <span class="purchase-order-status status-confirmed">已确认</span>
                                {% elif order.status == '准备入库' %}
                                <span class="purchase-order-status status-delivered">准备入库</span>
                                {% elif order.status == '已取消' %}
                                <span class="purchase-order-status status-cancelled">已取消</span>
                                {% else %}
                                <span class="purchase-order-status">{{ order.get_status_display() }}</span>
                                {% endif %}

                                {% if order.status_info %}
                                    <!-- 入库状态 -->
                                    {% if order.status_info.has_stock_in %}
                                        {% set stock_in_status = order.status_info.stock_in_status %}
                                        {% if stock_in_status == '已入库' %}
                                            <span class="badge badge-success ml-2">{{ stock_in_status }}</span>
                                        {% elif stock_in_status == '已审核' %}
                                            <span class="badge badge-info ml-2">{{ stock_in_status }}</span>
                                        {% elif stock_in_status == '待审核' %}
                                            <span class="badge badge-warning ml-2">{{ stock_in_status }}</span>
                                        {% elif stock_in_status == '已取消' %}
                                            <span class="badge badge-danger ml-2">{{ stock_in_status }}</span>
                                        {% else %}
                                            <span class="badge badge-info ml-2">{{ stock_in_status }}</span>
                                        {% endif %}
                                    {% else %}
                                        <span class="badge badge-secondary ml-2">未入库</span>
                                    {% endif %}

                                    <!-- 消耗状态 -->
                                    {% if order.status_info.consumption_status != '未消耗' %}
                                        <span class="badge {% if order.status_info.consumption_status == '已消耗' %}badge-success{% else %}badge-warning{% endif %} ml-2">
                                            {{ order.status_info.consumption_status }}
                                        </span>
                                    {% endif %}
                                {% endif %}
                            </p>

                            <!-- 消耗详情 -->
                            {% if order.status_info and order.status_info.consumption_details %}
                            <p>
                                <strong>消耗餐次：</strong>
                                <span class="text-muted">
                                    {% for detail in order.status_info.consumption_details %}
                                        {{ detail.consumption_date|format_datetime('%Y-%m-%d') }} {{ detail.meal_type }}{% if not loop.last %}, {% endif %}
                                    {% endfor %}
                                </span>
                            </p>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <p><strong>采购区域：</strong>{{ order.area_name if order.area_name else '-' }}</p>
                            <p><strong>预计送货日期：</strong>{{ order.expected_delivery_date|format_datetime('%Y-%m-%d') if order.expected_delivery_date else '-' }}</p>
                            <p><strong>实际送货日期：</strong>{{ order.delivery_date|format_datetime('%Y-%m-%d') if order.delivery_date else '-' }}</p>
                        </div>
                    </div>
                    {% if order.notes %}
                    <div class="row mt-3">
                        <div class="col-12">
                            <p><strong>备注：</strong>{{ order.notes }}</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">采购明细</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th style="width: 5%">序号</th>
                                    <th class="w-25">食材名称</th>
                                    <th style="width: 12%">采购量</th>
                                    <th style="width: 8%">单位</th>
                                    <th class="w-20">供应商</th>
                                    <th class="w-30">状态信息</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in order['order_items'] %}
                                <tr class="{% if order.status_info and order.status_info.items_status %}
                                    {% for item_status in order.status_info.items_status %}
                                        {% if item_status.ingredient_name == item.ingredient_name %}
                                            {% if item_status.consumption_status == '已消耗' %}table-light{% elif item_status.consumption_status == '部分消耗' %}table-warning{% endif %}
                                        {% endif %}
                                    {% endfor %}
                                {% endif %}">
                                    <td>{{ loop.index }}</td>
                                    <td>{{ item.ingredient_name }}</td>
                                    <td>{{ item.quantity }}</td>
                                    <td>{{ item.unit }}</td>
                                    <td>{{ item.supplier_name if item.supplier_name else '自购' }}</td>
                                    <td>
                                        <div class="d-flex flex-column">
                                            <!-- 入库状态 -->
                                            {% if order.status_info and order.status_info.has_stock_in %}
                                                {% set stock_in_status = order.status_info.stock_in_status %}
                                                {% if stock_in_status == '已入库' %}
                                                    <span class="badge badge-success mb-1" style="font-size: 0.75em;">
                                                        已入库 ({{ order.status_info.stock_in_date|format_datetime('%m-%d') if order.status_info.stock_in_date else '-' }})
                                                    </span>
                                                {% elif stock_in_status == '已审核' %}
                                                    <span class="badge badge-info mb-1" style="font-size: 0.75em;">
                                                        已审核 ({{ order.status_info.stock_in_date|format_datetime('%m-%d') if order.status_info.stock_in_date else '-' }})
                                                    </span>
                                                {% elif stock_in_status == '待审核' %}
                                                    <span class="badge badge-warning mb-1" style="font-size: 0.75em;">
                                                        待审核 ({{ order.status_info.stock_in_date|format_datetime('%m-%d') if order.status_info.stock_in_date else '-' }})
                                                    </span>
                                                {% elif stock_in_status == '已取消' %}
                                                    <span class="badge badge-danger mb-1" style="font-size: 0.75em;">
                                                        已取消 ({{ order.status_info.stock_in_date|format_datetime('%m-%d') if order.status_info.stock_in_date else '-' }})
                                                    </span>
                                                {% else %}
                                                    <span class="badge badge-info mb-1" style="font-size: 0.75em;">
                                                        {{ stock_in_status }} ({{ order.status_info.stock_in_date|format_datetime('%m-%d') if order.status_info.stock_in_date else '-' }})
                                                    </span>
                                                {% endif %}
                                            {% else %}
                                                <span class="badge badge-secondary mb-1" style="font-size: 0.75em;">未入库</span>
                                            {% endif %}

                                            <!-- 消耗状态 -->
                                            {% if order.status_info and order.status_info.items_status %}
                                                {% for item_status in order.status_info.items_status %}
                                                    {% if item_status.ingredient_name == item.ingredient_name %}
                                                        {% if item_status.consumption_status != '未消耗' %}
                                                            <span class="badge {% if item_status.consumption_status == '已消耗' %}badge-success{% else %}badge-warning{% endif %} mb-1" style="font-size: 0.75em;">
                                                                {{ item_status.consumption_status }}
                                                            </span>

                                                            <!-- 消耗餐次详情 -->
                                                            {% if item_status.consumption_meals %}
                                                                <small class="text-muted" style="font-size: 0.7em;">
                                                                    {% for meal in item_status.consumption_meals %}
                                                                        {{ meal.consumption_date|format_datetime('%m-%d') }} {{ meal.meal_type }}{% if not loop.last %}<br>{% endif %}
                                                                    {% endfor %}
                                                                </small>
                                                            {% endif %}
                                                        {% else %}
                                                            <span class="badge badge-light mb-1" style="font-size: 0.75em;">未消耗</span>
                                                        {% endif %}
                                                    {% endif %}
                                                {% endfor %}
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 订单状态时间线 -->
        <div class="col-md-4">
            <div class="card mb-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">订单状态时间线</h5>
                </div>
                <div class="card-body">
                    <div class="purchase-order-timeline">
                        <div class="purchase-order-timeline-item">
                            <div class="purchase-order-timeline-title">订单创建</div>
                            <div class="purchase-order-timeline-time">{{ order.order_date|format_datetime('%Y-%m-%d %H:%M') if order.order_date else '-' }}</div>
                        </div>
                        {% if order.confirmed_at %}
                        <div class="purchase-order-timeline-item">
                            <div class="purchase-order-timeline-title">订单确认</div>
                            <div class="purchase-order-timeline-time">{{ order.confirmed_at|format_datetime('%Y-%m-%d %H:%M') }}</div>
                        </div>
                        {% endif %}
                        {% if order.delivered_at %}
                        <div class="purchase-order-timeline-item">
                            <div class="purchase-order-timeline-title">已送达</div>
                            <div class="purchase-order-timeline-time">{{ order.delivered_at|format_datetime('%Y-%m-%d %H:%M') }}</div>
                            {% if order.delivery_notes %}
                            <div class="text-muted small">备注：{{ order.delivery_notes }}</div>
                            {% endif %}
                        </div>
                        {% endif %}
                        {% if order.cancelled_at %}
                        <div class="purchase-order-timeline-item pending">
                            <div class="purchase-order-timeline-title">已取消</div>
                            <div class="purchase-order-timeline-time">{{ order.cancelled_at|format_datetime('%Y-%m-%d %H:%M') }}</div>
                            {% if order.cancel_reason %}
                            <div class="text-muted small">原因：{{ order.cancel_reason }}</div>
                            {% endif %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 确认模态框 -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认订单</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>确定要确认这个采购订单吗？确认后将通知供应商开始备货。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" id="confirmOrderBtn">
                    <i class="fas fa-check"></i> 确认
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 取消模态框 -->
<div class="modal fade" id="cancelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">取消订单</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>确定要取消这个采购订单吗？取消后将无法恢复。</p>
                <div class="form-group">
                    <label for="cancelReason">取消原因：</label>
                    <textarea class="form-control" id="cancelReason" rows="3" required></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-danger" id="cancelOrderBtn">
                    <i class="fas fa-times"></i> 确定取消
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 送达模态框 -->
<div class="modal fade" id="deliverModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">标记送达</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>确认所有食材已送达并验收无误？标记送达后，订单将进入"准备入库"状态，您可以创建入库单。</p>
                <div class="form-group">
                    <label for="deliveryNotes">备注（可选）：</label>
                    <textarea class="form-control" id="deliveryNotes" rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-info" id="deliverOrderBtn">
                    <i class="fas fa-truck"></i> 确认送达
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 删除模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">删除订单</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> 警告：此操作不可恢复！
                </div>
                <p>确定要<strong>永久删除</strong>这个采购订单吗？删除后将无法恢复。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="deleteOrderBtn">
                    <i class="fas fa-trash-alt"></i> 确认删除
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    // 状态更新函数
    function updateOrderStatus(newStatus) {
        // 更新状态显示
        const statusElement = $('.purchase-order-status');
        statusElement.removeClass('status-pending status-confirmed status-delivered status-cancelled');

        let statusClass = '';
        let statusText = newStatus;

        switch(newStatus) {
            case '待确认':
                statusClass = 'status-pending';
                break;
            case '已确认':
                statusClass = 'status-confirmed';
                break;
            case '准备入库':
                statusClass = 'status-delivered';
                break;
            case '已取消':
                statusClass = 'status-cancelled';
                break;
        }

        statusElement.addClass(statusClass).text(statusText);

        // 更新操作按钮的可见性
        updateActionButtons(newStatus);

        // 更新流程步骤
        updateProcessSteps(newStatus);
    }

    function updateActionButtons(status) {
        $('.action-buttons .btn').hide();

        switch(status) {
            case '待确认':
                $('.confirm-btn, .cancel-btn, .delete-btn').show();
                break;
            case '已确认':
                $('.deliver-btn, .cancel-btn').show();
                break;
            case '准备入库':
                $('.create-stock-in-btn').show();
                break;
            case '已取消':
                $('.delete-btn').show();
                break;
        }
    }

    function updateProcessSteps(status) {
        $('.compact-process-step').removeClass('active completed');

        switch(status) {
            case '待确认':
                $('.compact-process-step[data-step="pending"]').addClass('active');
                break;
            case '已确认':
                $('.compact-process-step[data-step="pending"]').addClass('completed');
                $('.compact-process-step[data-step="confirmed"]').addClass('active');
                break;
            case '准备入库':
                $('.compact-process-step[data-step="pending"], .compact-process-step[data-step="confirmed"]').addClass('completed');
                $('.compact-process-step[data-step="delivered"]').addClass('active');
                break;
            case '已取消':
                $('.compact-process-step').removeClass('active completed');
                $('.compact-process-step[data-step="cancelled"]').addClass('active');
                break;
        }
    }

    function showSuccessMessage(message) {
        // 显示成功消息
        const alertHtml = `
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `;
        $('.card-body').first().prepend(alertHtml);

        // 3秒后自动消失
        setTimeout(function() {
            $('.alert-success').alert('close');
        }, 3000);
    }

    // 紧凑流程步骤点击事件
    $('.compact-process-step[data-action]').click(function() {
        const action = $(this).data('action');

        if (action === 'confirm') {
            $('#confirmModal').modal('show');
        } else if (action === 'deliver') {
            $('#deliverModal').modal('show');
        } else if (action === 'stock-in') {
            window.location.href = '{{ url_for("stock_in.create_from_purchase_order", order_id=order.id) }}';
        }
    });

    // 确认订单
    $('.confirm-btn').click(function() {
        $('#confirmModal').modal('show');
    });

    $('#confirmOrderBtn').click(function() {
        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 处理中...');

        $.ajax({
            url: '{{ url_for("purchase_order.confirm_order", id=order.id) }}',
            type: 'POST',
            success: function(response) {
                if (response.success) {
                    updateOrderStatus('已确认');
                    showSuccessMessage(response.message);
                    $('#confirmModal').modal('hide');
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('确认订单失败，请重试');
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-check"></i> 确认');
            }
        });
    });

    // 取消订单
    $('.cancel-btn').click(function() {
        $('#cancelModal').modal('show');
    });

    $('#cancelOrderBtn').click(function() {
        const reason = $('#cancelReason').val().trim();
        if (!reason) {
            alert('请输入取消原因');
            return;
        }

        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 处理中...');

        $.ajax({
            url: '{{ url_for("purchase_order.cancel_order", id=order.id) }}',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ reason: reason }),
            success: function(response) {
                if (response.success) {
                    updateOrderStatus('已取消');
                    showSuccessMessage(response.message);
                    $('#cancelModal').modal('hide');
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('取消订单失败，请重试');
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-times"></i> 确定取消');
            }
        });
    });

    // 标记送达
    $('.deliver-btn, .compact-process-step[data-action="deliver"]').click(function() {
        $('#deliverModal').modal('show');
    });

    $('#deliverOrderBtn').click(function() {
        const notes = $('#deliveryNotes').val().trim();
        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 处理中...');

        $.ajax({
            url: '{{ url_for("purchase_order.deliver_order", id=order.id) }}',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ notes: notes }),
            success: function(response) {
                if (response.success) {
                    updateOrderStatus('准备入库');
                    showSuccessMessage(response.message);
                    $('#deliverModal').modal('hide');
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('标记送达失败，请重试');
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-truck"></i> 确认送达');
            }
        });
    });

    // 删除订单
    $('.delete-btn').click(function() {
        $('#deleteModal').modal('show');
    });

    $('#deleteOrderBtn').click(function() {
        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 处理中...');

        $.ajax({
            url: '{{ url_for("purchase_order.delete_order", id=order.id) }}',
            type: 'POST',
            success: function(response) {
                if (response.success) {
                    // 删除成功后跳转到列表页
                    window.location.href = '{{ url_for("purchase_order.index") }}';
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('删除订单失败，请重试');
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-trash-alt"></i> 确认删除');
                $('#deleteModal').modal('hide');
            }
        });
    });
});
</script>
{% endblock %}
