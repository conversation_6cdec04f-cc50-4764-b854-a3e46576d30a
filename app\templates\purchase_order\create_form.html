{% extends 'base.html' %}

{% block title %}创建采购订单 - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
  .ingredient-row {
    margin-bottom: 10px;
    padding: 15px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background-color: #ffffff;
  }
  .ingredient-row:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
  }
  .supplier-select {
    width: 100%;
  }
  .quantity-input, .unit-price-input {
    width: 100%;
  }

  /* 修复颜色问题，使用系统主题 */
  .card-header {
    background-color: #f8f9fa !important;
    border-bottom: 1px solid #dee2e6 !important;
  }

  .card-header h6 {
    color: #495057 !important;
  }

  .btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    color: #ffffff;
  }

  .btn-primary:hover {
    background-color: #0056b3;
    border-color: #004085;
  }

  .form-label {
    color: #495057;
    font-weight: 500;
  }

  .text-primary {
    color: #007bff !important;
  }

  /* 隐藏区域选择字段 */
  .area-field-hidden {
    display: none;
  }

  /* 食材选择模态框样式 */
  .ingredient-tabs-container {
    max-height: 500px;
    overflow-y: auto;
  }

  /* 标签页导航样式 */
  .nav-pills .nav-link {
    border-radius: 20px;
    padding: 8px 12px;
    font-size: 0.9rem;
    margin: 0 2px;
  }

  .nav-pills .nav-link.active {
    background-color: #007bff;
  }

  .nav-pills .nav-link:not(.active) {
    color: #6c757d;
    background-color: #f8f9fa;
  }

  .nav-pills .nav-link:not(.active):hover {
    background-color: #e9ecef;
    color: #495057;
  }

  /* 食材网格布局 */
  .ingredients-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 8px;
    max-height: 350px;
    overflow-y: auto;
    padding: 5px;
  }

  .ingredient-item {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 8px;
    background-color: #ffffff;
    transition: all 0.2s ease;
    cursor: pointer;
  }

  .ingredient-item:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,123,255,0.1);
  }

  .ingredient-item .custom-control {
    margin: 0;
  }

  .ingredient-item .custom-control-label {
    cursor: pointer;
    width: 100%;
    padding-left: 0;
    margin: 0;
    position: relative;
  }

  .ingredient-item .custom-control-label::before,
  .ingredient-item .custom-control-label::after {
    position: absolute;
    top: 2px;
    left: 0;
  }

  .ingredient-item .custom-control-input:checked ~ .custom-control-label {
    color: #007bff;
  }

  .ingredient-item .custom-control-input:checked ~ .custom-control-label .ingredient-info strong {
    color: #007bff;
  }

  .ingredient-info {
    padding-left: 1.8rem;
    min-height: 20px;
  }

  .ingredient-info strong {
    font-size: 0.9rem;
    display: block;
    margin-bottom: 2px;
  }

  .ingredient-info small {
    font-size: 0.75rem;
    line-height: 1.2;
  }

  /* 标签和按钮样式 */
  .badge {
    font-size: 0.7rem;
    padding: 2px 6px;
  }

  .select-all-ingredients,
  .select-category-all {
    font-size: 0.8rem;
    padding: 4px 8px;
  }

  /* 模态框大小调整 */
  .modal-lg {
    max-width: 800px;
  }

  /* 搜索框样式 */
  #ingredient-search {
    border-radius: 20px;
    padding-left: 15px;
  }

  /* 标签页内容区域 */
  .tab-content {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    background-color: #fafafa;
  }

  /* 滚动条样式 */
  .ingredients-grid::-webkit-scrollbar,
  .ingredient-tabs-container::-webkit-scrollbar {
    width: 6px;
  }

  .ingredients-grid::-webkit-scrollbar-track,
  .ingredient-tabs-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .ingredients-grid::-webkit-scrollbar-thumb,
  .ingredient-tabs-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  .ingredients-grid::-webkit-scrollbar-thumb:hover,
  .ingredient-tabs-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row mb-4">
    <div class="col-md-8">
      <h2>创建采购订单</h2>
      <p class="text-muted">填写采购订单信息并提交</p>
    </div>
    <div class="col-md-4 text-right">
      <a href="{{ url_for('purchase_order.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> 返回列表
      </a>
    </div>
  </div>

  <div class="card shadow mb-4">
    <div class="card-header py-3">
      <h6 class="m-0 font-weight-bold text-primary">采购订单信息</h6>
    </div>
    <div class="card-body">
      <form method="post" id="purchase-order-form">
        {{ form.csrf_token }}

        <!-- 隐藏区域选择，自动绑定用户学校 -->
        <div class="area-field-hidden">
          {{ form.area_id() }}
        </div>

        <!-- 显示学校信息 -->
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label class="form-label">所属学校</label>
              <input type="text" class="form-control" value="{{ user_area.name }}" readonly>
              <small class="form-text text-muted">采购订单将自动绑定到您的学校</small>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              {{ form.supplier_id.label(class="form-label") }}
              {{ form.supplier_id(class="form-control") }}
              {% if form.supplier_id.errors %}
                <div class="invalid-feedback d-block">
                  {% for error in form.supplier_id.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
              {% endif %}
              <small class="form-text text-muted">默认为自购，也可选择合作供应商</small>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-4">
            <div class="form-group">
              {{ form.order_date.label(class="form-label") }}
              {{ form.order_date(class="form-control", type="date") }}
              {% if form.order_date.errors %}
                <div class="invalid-feedback d-block">
                  {% for error in form.order_date.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
              {% endif %}
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-group">
              {{ form.expected_delivery_date.label(class="form-label") }}
              {{ form.expected_delivery_date(class="form-control", type="date") }}
              {% if form.expected_delivery_date.errors %}
                <div class="invalid-feedback d-block">
                  {% for error in form.expected_delivery_date.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
              {% endif %}
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-group">
              {{ form.batch_number.label(class="form-label") }}
              {{ form.batch_number(class="form-control", readonly=true) }}
              {% if form.batch_number.errors %}
                <div class="invalid-feedback d-block">
                  {% for error in form.batch_number.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
              {% endif %}
              <small class="form-text text-muted">系统自动生成</small>
            </div>
          </div>
        </div>

        <div class="form-group">
          {{ form.notes.label(class="form-label") }}
          {{ form.notes(class="form-control", rows=3) }}
          {% if form.notes.errors %}
            <div class="invalid-feedback d-block">
              {% for error in form.notes.errors %}
                {{ error }}
              {% endfor %}
            </div>
          {% endif %}
        </div>

        <div class="card shadow mb-4">
          <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">采购明细</h6>
            <button type="button" class="btn btn-sm btn-primary" id="add-ingredient">
              <i class="fas fa-plus"></i> 添加食材
            </button>
          </div>
          <div class="card-body">
            <div id="ingredients-container">
              <!-- 默认不显示任何食材行，用户点击添加按钮后才显示 -->
              <div id="no-ingredients-message" class="text-center text-muted py-4">
                <i class="fas fa-plus-circle fa-3x mb-3"></i>
                <p>暂无食材，请点击"添加食材"按钮开始添加</p>
              </div>
            </div>

            <div class="text-right mt-3">
              <h5>总金额: <span id="total-amount">0.00</span> 元</h5>
            </div>
          </div>
        </div>

        <div class="text-center">
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-save"></i> 创建采购订单
          </button>
          <a href="{{ url_for('purchase_order.index') }}" class="btn btn-secondary">
            <i class="fas fa-times"></i> 取消
          </a>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- 食材选择模态框 -->
<div class="modal fade" id="ingredientModal" tabindex="-1" role="dialog" aria-labelledby="ingredientModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="ingredientModalLabel">选择食材</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <!-- 搜索栏 -->
        <div class="row mb-3">
          <div class="col-12">
            <input type="text" class="form-control" id="ingredient-search" placeholder="🔍 搜索食材名称...">
          </div>
        </div>

        <!-- 食材选择界面 -->
        <div class="ingredient-selection-container">
          <!-- 控制栏 -->
          <div class="row mb-3">
            <div class="col-md-4">
              <select class="form-control form-control-sm" id="category-filter">
                <option value="">加载中...</option>
              </select>
            </div>
            <div class="col-md-4">
              <button type="button" class="btn btn-primary btn-sm" id="load-ingredients">
                <i class="fas fa-sync"></i> 加载食材
              </button>
            </div>
            <div class="col-md-4">
              <button type="button" class="btn btn-outline-success btn-sm select-all-ingredients" disabled>
                <i class="fas fa-check-double"></i> 全选当前页
              </button>
            </div>
          </div>

          <!-- 食材列表容器 -->
          <div id="ingredients-list-container">
            <div class="text-center text-muted py-4">
              <i class="fas fa-info-circle fa-2x mb-2"></i>
              <p>点击"加载食材"按钮开始选择食材</p>
              <small>为了提高页面加载速度，食材数据采用按需加载</small>
            </div>
          </div>

          <!-- 分页控件 -->
          <div id="pagination-container" class="d-flex justify-content-between align-items-center mt-3" style="display: none;">
            <div>
              <small class="text-muted" id="pagination-info"></small>
            </div>
            <div>
              <button type="button" class="btn btn-sm btn-outline-secondary" id="prev-page" disabled>
                <i class="fas fa-chevron-left"></i> 上一页
              </button>
              <span class="mx-2" id="page-info"></span>
              <button type="button" class="btn btn-sm btn-outline-secondary" id="next-page" disabled>
                下一页 <i class="fas fa-chevron-right"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" id="add-selected-ingredients">添加选中的食材</button>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
  $(document).ready(function() {
    // 全局变量
    let currentPage = 1;
    let totalPages = 1;
    let currentCategory = '';
    let ingredientsLoaded = false;

    // 初始化分类选择器
    initializeCategories();

    // 检查是否有来自消耗计划的预填充数据
    checkAndLoadPrefillData();

    // 计算总金额
    function calculateTotalAmount() {
      let total = 0;
      $('.ingredient-row').each(function() {
        const totalPrice = parseFloat($(this).find('input[name$="total_price"]').val()) || 0;
        total += totalPrice;
      });
      $('#total-amount').text(total.toFixed(2));
    }

    // 更新空食材提示的显示状态
    function updateNoIngredientsMessage() {
      if ($('.ingredient-row').length === 0) {
        $('#no-ingredients-message').show();
      } else {
        $('#no-ingredients-message').hide();
      }
    }

    // 计算行总价
    function calculateRowTotal(row) {
      const quantity = parseFloat($(row).find('.quantity-input').val()) || 0;
      const unitPrice = parseFloat($(row).find('.unit-price-input').val()) || 0;
      const totalPrice = quantity * unitPrice;
      $(row).find('input[name$="total_price"]').val(totalPrice.toFixed(2));
      calculateTotalAmount();
    }

    // 初始计算总金额和更新提示信息
    calculateTotalAmount();
    updateNoIngredientsMessage();

    // 监听数量和单价变化
    $(document).on('input', '.quantity-input, .unit-price-input', function() {
      calculateRowTotal($(this).closest('.ingredient-row'));
    });

    // 初始化分类选择器
    function initializeCategories() {
      const categorySelect = $('#category-filter');
      categorySelect.html(`
        <option value="">全部</option>
        <option value="蔬菜">蔬菜</option>
        <option value="肉类">肉类</option>
        <option value="主食">主食</option>
        <option value="调料">调料</option>
        <option value="其他">其他</option>
      `);
    }

    // 加载食材数据（简化版本）
    function loadIngredients(page = 1, category = '') {
      const container = $('#ingredients-list-container');

      // 显示加载状态
      container.html(`
        <div class="text-center py-4">
          <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
          <p>正在加载食材数据...</p>
        </div>
      `);

      // 使用现有的食材API
      $.ajax({
        url: '/ingredient/api',
        method: 'GET',
        success: function(data) {
          if (data && data.length > 0) {
            // 过滤数据
            let filteredData = data;
            if (category && category !== '全部') {
              filteredData = data.filter(item => item.category === category);
            }

            // 搜索过滤
            const searchTerm = $('#ingredient-search').val();
            if (searchTerm) {
              filteredData = filteredData.filter(item =>
                item.name.toLowerCase().includes(searchTerm.toLowerCase())
              );
            }

            // 分页
            const perPage = 20;
            const startIndex = (page - 1) * perPage;
            const endIndex = startIndex + perPage;
            const pageData = filteredData.slice(startIndex, endIndex);

            currentPage = page;
            totalPages = Math.ceil(filteredData.length / perPage);

            // 渲染食材列表
            renderIngredients(pageData);

            // 更新分页信息
            updatePagination({
              page: page,
              pages: totalPages,
              per_page: perPage,
              total: filteredData.length,
              has_prev: page > 1,
              has_next: page < totalPages
            });

            // 启用全选按钮
            $('.select-all-ingredients').prop('disabled', false);

            ingredientsLoaded = true;
          } else {
            container.html(`
              <div class="alert alert-warning">
                <i class="fas fa-info-circle"></i> 暂无食材数据
              </div>
            `);
          }
        },
        error: function() {
          container.html(`
            <div class="alert alert-danger">
              <i class="fas fa-exclamation-triangle"></i> 加载食材数据失败，请重试
            </div>
          `);
        }
      });
    }

    // 渲染食材列表
    function renderIngredients(ingredients) {
      const container = $('#ingredients-list-container');

      if (ingredients.length === 0) {
        container.html(`
          <div class="text-center text-muted py-4">
            <i class="fas fa-search fa-2x mb-2"></i>
            <p>没有找到符合条件的食材</p>
          </div>
        `);
        return;
      }

      let html = '<div class="ingredients-grid">';
      ingredients.forEach(function(ingredient) {
        html += `
          <div class="ingredient-item" data-id="${ingredient.id || ''}" data-name="${ingredient.name || ''}" data-unit="${ingredient.unit || ''}" data-category="${ingredient.category || ''}">
            <div class="custom-control custom-checkbox">
              <input type="checkbox" class="custom-control-input ingredient-checkbox" id="ingredient-${ingredient.id || Math.random()}" value="${ingredient.id || ''}">
              <label class="custom-control-label" for="ingredient-${ingredient.id || Math.random()}">
                <div class="ingredient-info">
                  <strong>${ingredient.name || '未知食材'}</strong>
                  <small class="text-muted d-block">${ingredient.unit || ''}</small>
                </div>
              </label>
            </div>
          </div>
        `;
      });
      html += '</div>';

      container.html(html);
    }

    // 更新分页信息
    function updatePagination(pagination) {
      $('#pagination-info').text(`显示第 ${(pagination.page - 1) * pagination.per_page + 1} - ${Math.min(pagination.page * pagination.per_page, pagination.total)} 条，共 ${pagination.total} 条`);
      $('#page-info').text(`${pagination.page} / ${pagination.pages}`);

      $('#prev-page').prop('disabled', !pagination.has_prev);
      $('#next-page').prop('disabled', !pagination.has_next);

      $('#pagination-container').show();
    }

    // 添加食材按钮
    $('#add-ingredient').click(function() {
      $('#ingredientModal').modal('show');

      // 如果还没有加载过食材，自动加载
      if (!ingredientsLoaded) {
        loadIngredients();
      }
    });

    // 移除食材按钮
    $(document).on('click', '.remove-ingredient', function() {
      $(this).closest('.ingredient-row').remove();
      calculateTotalAmount();
      updateNoIngredientsMessage();
    });

    // 事件监听器
    $('#load-ingredients').click(function() {
      currentCategory = $('#category-filter').val();
      loadIngredients(1, currentCategory);
    });

    $('#category-filter').change(function() {
      if (ingredientsLoaded) {
        currentCategory = $(this).val();
        loadIngredients(1, currentCategory);
      }
    });

    $('#prev-page').click(function() {
      if (currentPage > 1) {
        loadIngredients(currentPage - 1, currentCategory);
      }
    });

    $('#next-page').click(function() {
      if (currentPage < totalPages) {
        loadIngredients(currentPage + 1, currentCategory);
      }
    });

    // 食材搜索（实时搜索）
    let searchTimeout;
    $('#ingredient-search').on('keyup', function() {
      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(function() {
        if (ingredientsLoaded) {
          loadIngredients(1, currentCategory);
        }
      }, 500);
    });

    // 全选当前页食材
    $(document).on('click', '.select-all-ingredients', function() {
      const checkboxes = $('#ingredients-list-container .ingredient-checkbox');
      const allChecked = checkboxes.filter(':checked').length === checkboxes.length;

      // 如果全部选中则取消全选，否则全选
      checkboxes.prop('checked', !allChecked);

      // 更新按钮文字
      $(this).html(allChecked ? '<i class="fas fa-check-double"></i> 全选当前页' : '<i class="fas fa-times"></i> 取消全选');
    });



    // 点击食材项选择/取消选择（排除checkbox和label点击）
    $(document).on('click', '.ingredient-item', function(e) {
      // 如果点击的是checkbox、label或者label内的元素，不处理
      if ($(e.target).is('input[type="checkbox"]') ||
          $(e.target).is('label') ||
          $(e.target).closest('label').length > 0) {
        return;
      }

      // 点击其他区域时切换checkbox状态
      const checkbox = $(this).find('.ingredient-checkbox');
      checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
    });

    // 确保checkbox点击事件正常工作
    $(document).on('change', '.ingredient-checkbox', function(e) {
      // 阻止事件冒泡，避免触发父元素的点击事件
      e.stopPropagation();
    });

    // 注释掉不存在的标签页事件
    // $('a[data-toggle="pill"]').on('shown.bs.tab', function (e) {
    //   $('#ingredient-search').val('');
    //   $('.ingredient-item').show();
    // });

    // 添加选中的食材
    $('#add-selected-ingredients').click(function() {
      const selectedIngredients = [];

      $('.ingredient-item').each(function() {
        if ($(this).find('.ingredient-checkbox').prop('checked')) {
          selectedIngredients.push({
            id: $(this).data('id'),
            name: $(this).data('name'),
            unit: $(this).data('unit')
          });
        }
      });

      if (selectedIngredients.length > 0) {
        for (const ingredient of selectedIngredients) {
          addIngredientRow(ingredient);
        }
        $('#ingredientModal').modal('hide');
        // 重置复选框和按钮文字
        $('.ingredient-checkbox').prop('checked', false);
        $('.select-all-ingredients').html('<i class="fas fa-check-double"></i> 全选当前页');
        updateNoIngredientsMessage();
      } else {
        alert('请至少选择一种食材');
      }
    });

    // 添加食材行
    function addIngredientRow(ingredient) {
      const index = $('.ingredient-row').length;
      const template = `
        <div class="ingredient-row">
          <div class="row">
            <div class="col-md-3">
              <label class="form-label">食材名称</label>
              <input type="text" class="form-control" name="items-${index}-ingredient_name" value="${ingredient.name}" readonly>
              <input type="hidden" name="items-${index}-ingredient_id" value="${ingredient.id}">
            </div>
            <div class="col-md-2">
              <label class="form-label">数量</label>
              <input type="number" class="form-control quantity-input" name="items-${index}-quantity" value="1" min="0.01" step="0.01">
            </div>
            <div class="col-md-1">
              <label class="form-label">单位</label>
              <input type="text" class="form-control" name="items-${index}-unit" value="${ingredient.unit}" readonly>
            </div>
            <div class="col-md-2">
              <label class="form-label">单价</label>
              <input type="number" class="form-control unit-price-input" name="items-${index}-unit_price" value="0" min="0" step="0.01">
            </div>
            <div class="col-md-2">
              <label class="form-label">总价</label>
              <input type="text" class="form-control" name="items-${index}-total_price" value="0.00" readonly>
            </div>
            <div class="col-md-2">
              <label class="form-label">&nbsp;</label>
              <button type="button" class="btn btn-danger btn-block remove-ingredient">
                <i class="fas fa-trash"></i> 移除
              </button>
            </div>
          </div>
          <div class="row mt-2">
            <div class="col-md-6">
              <label class="form-label">供应商</label>
              <select class="form-control" name="items-${index}-supplier_id">
                {% for value, label in form.supplier_id.choices %}
                  <option value="{{ value }}">{{ label }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="col-md-6">
              <label class="form-label">备注</label>
              <input type="text" class="form-control" name="items-${index}-notes">
            </div>
          </div>
          <input type="hidden" name="items-${index}-product_id" value="">
          <hr class="my-3">
        </div>
      `;

      $('#ingredients-container').append(template);
      calculateRowTotal($('.ingredient-row').last());
    }

    // 表单提交前验证
    $('#purchase-order-form').submit(function(e) {
      if ($('.ingredient-row').length === 0) {
        e.preventDefault();
        alert('请至少添加一种食材');
        return false;
      }

      // 检查数量和单价
      let valid = true;
      $('.ingredient-row').each(function() {
        const quantity = parseFloat($(this).find('.quantity-input').val()) || 0;
        const unitPrice = parseFloat($(this).find('.unit-price-input').val()) || 0;

        if (quantity <= 0) {
          valid = false;
          $(this).find('.quantity-input').addClass('is-invalid');
        } else {
          $(this).find('.quantity-input').removeClass('is-invalid');
        }

        if (unitPrice < 0) {
          valid = false;
          $(this).find('.unit-price-input').addClass('is-invalid');
        } else {
          $(this).find('.unit-price-input').removeClass('is-invalid');
        }
      });

      if (!valid) {
        e.preventDefault();
        alert('请检查数量和单价');
        return false;
      }
    });

    // 检查并加载预填充数据
    function checkAndLoadPrefillData() {
      // 检查URL参数
      const urlParams = new URLSearchParams(window.location.search);
      const fromParam = urlParams.get('from');

      if (fromParam === 'consumption-plan') {
        // 从sessionStorage获取数据
        const purchaseDataStr = sessionStorage.getItem('purchase_order_data');
        if (purchaseDataStr) {
          try {
            const purchaseData = JSON.parse(purchaseDataStr);
            console.log('加载预填充数据:', purchaseData);

            // 显示来源信息
            showPrefillInfo(purchaseData);

            // 预填充食材
            if (purchaseData.missing_ingredients && purchaseData.missing_ingredients.length > 0) {
              prefillIngredients(purchaseData.missing_ingredients);
            }

            // 清除sessionStorage中的数据
            sessionStorage.removeItem('purchase_order_data');
          } catch (e) {
            console.error('解析预填充数据失败:', e);
          }
        }
      }
    }

    // 显示预填充信息
    function showPrefillInfo(purchaseData) {
      const infoHtml = `
        <div class="alert alert-info alert-dismissible fade show" role="alert">
          <h6><i class="fas fa-info-circle"></i> 来自消耗计划的采购需求</h6>
          <p class="mb-1">
            <strong>消耗日期:</strong> ${purchaseData.consumption_date}<br>
            <strong>餐次:</strong> ${purchaseData.meal_types.join(', ')}<br>
            <strong>缺少食材:</strong> ${purchaseData.missing_ingredients.length} 种
          </p>
          <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
      `;

      // 在表单前插入信息
      $('.card-body').first().prepend(infoHtml);
    }

    // 预填充食材
    function prefillIngredients(missingIngredients) {
      // 隐藏"暂无食材"提示
      $('#no-ingredients-message').hide();

      console.log('开始预填充食材:', missingIngredients);

      missingIngredients.forEach(function(ingredient, index) {
        // 直接使用常见的标准单位
        ingredient.standard_unit = getStandardUnit(ingredient.name);
        addPrefillIngredientRow(ingredient);
      });
    }

    // 获取食材的标准单位
    function getStandardUnit(ingredientName) {
      // 常见食材的标准单位映射
      const unitMap = {
        '土豆': '公斤', '胡萝卜': '公斤', '白菜': '公斤', '萝卜': '公斤',
        '猪肉': '公斤', '牛肉': '公斤', '鸡肉': '公斤', '鱼肉': '公斤',
        '大米': '公斤', '面粉': '公斤', '油': '升', '盐': '公斤',
        '鸡蛋': '个', '牛奶': '升', '豆腐': '块', '面条': '公斤'
      };

      // 根据食材名称匹配标准单位
      for (let key in unitMap) {
        if (ingredientName.includes(key)) {
          return unitMap[key];
        }
      }

      // 默认返回公斤
      return '公斤';
    }

    // 添加预填充食材行（专用于从消耗计划预填充）
    function addPrefillIngredientRow(ingredient) {
      console.log('添加预填充食材行:', ingredient);

      const index = $('.ingredient-row').length;
      const template = `
        <div class="ingredient-row">
          <div class="row">
            <div class="col-md-3">
              <label class="form-label">食材名称</label>
              <input type="text" class="form-control" name="items-${index}-ingredient_name" value="${ingredient.name}" required>
              <input type="hidden" name="items-${index}-ingredient_id" value="">
              <input type="hidden" name="items-${index}-product_id" value="">
              <small class="text-muted">来自消耗计划分析</small>
            </div>
            <div class="col-md-2">
              <label class="form-label">数量</label>
              <input type="number" class="form-control quantity-input" name="items-${index}-quantity" value="${ingredient.shortage_quantity || 1}" min="0.01" step="0.01" required>
            </div>
            <div class="col-md-1">
              <label class="form-label">单位</label>
              <input type="text" class="form-control" name="items-${index}-unit" value="${ingredient.standard_unit || ingredient.unit || ''}" required>
              <small class="text-muted">标准单位</small>
            </div>
            <div class="col-md-2">
              <label class="form-label">单价</label>
              <input type="number" class="form-control unit-price-input" name="items-${index}-unit_price" value="0" min="0" step="0.01" required>
            </div>
            <div class="col-md-2">
              <label class="form-label">总价</label>
              <input type="number" class="form-control" name="items-${index}-total_price" value="0" readonly>
            </div>
            <div class="col-md-2">
              <label class="form-label">操作</label>
              <button type="button" class="btn btn-danger btn-sm remove-ingredient">
                <i class="fas fa-trash"></i> 移除
              </button>
            </div>
          </div>
          <div class="row mt-2">
            <div class="col-md-6">
              <label class="form-label">供应商</label>
              <select class="form-control" name="items-${index}-supplier_id">
                <option value="">自购</option>
                {% for value, label in form.supplier_id.choices %}
                  {% if value and value != 0 %}
                  <option value="{{ value }}">{{ label }}</option>
                  {% endif %}
                {% endfor %}
              </select>
            </div>
            <div class="col-md-6">
              <label class="form-label">备注</label>
              <input type="text" class="form-control" name="items-${index}-notes" value="来自消耗计划：${ingredient.details ? ingredient.details.map(d => d.meal_type + '(' + d.recipe_name + ')').join(', ') : ''}">
            </div>
          </div>
          <input type="hidden" name="items-${index}-product_id" value="">
          <hr class="my-3">
        </div>
      `;

      $('#ingredients-container').append(template);

      // 计算总价
      const $newRow = $('.ingredient-row').last();
      calculateRowTotal($newRow);

      console.log('预填充食材行添加完成');
    }

  });
</script>
{% endblock %}
